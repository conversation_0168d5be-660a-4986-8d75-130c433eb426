@echo off
setlocal enabledelayedexpansion

REM MyAdaptiveCloud Development Server Manager (Windows Batch Version)
REM Manages both .NET backend and Angular frontend applications

set "PROJECT_ROOT=%cd%"
set "BACKEND_PROJECT=src\MyAdaptiveCloud"
set "FRONTEND_DIR=src\MyAdaptiveCloud\ClientApp"
set "BACKEND_URL=https://localhost:5001"

REM Process tracking
set "BACKEND_PID="
set "FRONTEND_PID="

REM Function to display usage information
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help
if "%1"=="/?" goto :show_help

REM Check command line arguments
if "%1"=="-b" goto :start_backend_only
if "%1"=="--backend" goto :start_backend_only
if "%1"=="-f" goto :start_frontend_only
if "%1"=="--frontend" goto :start_frontend_only
if "%1"=="" goto :start_both
goto :unknown_option

:show_help
echo MyAdaptiveCloud Development Server Manager
echo.
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   (no args)     Start both backend and frontend applications
echo   -b, --backend Start only the .NET backend API
echo   -f, --frontend Start only the Angular frontend
echo   -h, --help    Show this help message
echo.
echo Default URLs:
echo   Backend API:  https://localhost:5001
echo   Frontend:     http://localhost:4200 (or next available port)
echo.
echo Press Ctrl+C to stop all running processes
goto :eof

:check_directory
if not exist "%~1" (
    echo Error: %~2 directory not found: %~1
    echo Please run this script from the project root directory.
    exit /b 1
)
goto :eof

:start_backend
echo Starting .NET Backend API...

REM Check if backend directory exists
call :check_directory "%BACKEND_PROJECT%" "Backend project"
if errorlevel 1 exit /b 1

echo Running: dotnet run --project "%BACKEND_PROJECT%"
start "MyAdaptiveCloud Backend" /min cmd /c "dotnet run --project \"%BACKEND_PROJECT%\""

echo Backend started
echo   URL: %BACKEND_URL%
echo.

REM Give backend time to start
timeout /t 3 /nobreak >nul
goto :eof

:start_frontend
echo Starting Angular Frontend...

REM Check if frontend directory exists
call :check_directory "%FRONTEND_DIR%" "Frontend"
if errorlevel 1 exit /b 1

REM Check if ng command is available
where ng >nul 2>&1
if errorlevel 1 (
    echo Error: Angular CLI (ng) not found
    echo Please install Angular CLI: npm install -g @angular/cli
    exit /b 1
)

echo Running: ng serve (in %FRONTEND_DIR%)
cd /d "%FRONTEND_DIR%"

REM Start frontend in a new window
start "MyAdaptiveCloud Frontend" /min cmd /c "echo n | ng serve"

cd /d "%PROJECT_ROOT%"

echo Frontend started
echo   Building... (this may take a moment)
echo   Check the frontend window for the actual URL
echo.

REM Give frontend time to start
timeout /t 5 /nobreak >nul
goto :eof

:start_backend_only
echo Starting backend only...
call :start_backend
if errorlevel 1 exit /b 1
echo Backend is running. Close the backend window to stop.
pause
goto :eof

:start_frontend_only
echo Starting frontend only...
call :start_frontend
if errorlevel 1 exit /b 1
echo Frontend is running. Close the frontend window to stop.
pause
goto :eof

:start_both
echo Starting MyAdaptiveCloud Development Environment...
echo.

REM Start backend first
call :start_backend
if errorlevel 1 (
    echo Failed to start backend. Aborting.
    exit /b 1
)

REM Start frontend
call :start_frontend
if errorlevel 1 (
    echo Failed to start frontend. Backend will continue running.
    echo Close the backend window to stop it.
    pause
    exit /b 1
)

echo Both applications are starting up!
echo.
echo Application URLs:
echo   Backend API:  %BACKEND_URL%
echo   Frontend:     Check the frontend window for actual URL
echo.
echo Close this window or press any key to continue...
echo (Applications will continue running in their own windows)
pause >nul
goto :eof

:unknown_option
echo Error: Unknown option '%1'
echo.
call :show_help
exit /b 1

:eof
