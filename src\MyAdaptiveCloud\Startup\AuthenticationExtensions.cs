﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.Net.Http.Headers;
using MyAdaptiveCloud.Api.Authorization.Basic;
using MyAdaptiveCloud.Api.Authorization.HMACSHA256;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Data.MyAdaptiveCloud;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Startup
{
    public static class AuthenticationExtensions
    {
        public static IServiceCollection ConfigureAuthentication(this IServiceCollection services, IConfigurationService configurationService)
        {
            const string AuthenticationMethodName = "MYAC-HMAC-SHA256";
            const string DefaultSchemaName = "SHA256_COOKIE";

            services.AddAuthentication(options =>
                {
                    options.DefaultScheme = DefaultSchemaName;
                    options.DefaultChallengeScheme = DefaultSchemaName;
                })
                .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
                {
                    options.LoginPath = "/local-authentication/login";
                    options.LogoutPath = "/user/logout";
                    options.SlidingExpiration = true;
                    options.Events.OnRedirectToLogin = context =>
                    {
                        context.Response.Headers.Location = context.RedirectUri;
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        return Task.CompletedTask;
                    };
                    options.Events.OnRedirectToAccessDenied = context =>
                    {
                        context.Response.Headers.Location = context.RedirectUri;
                        context.Response.StatusCode = StatusCodes.Status403Forbidden;
                        return Task.CompletedTask;
                    };
                    options.Events.OnCheckSlidingExpiration = async context =>
                    {
                        if (context.ShouldRenew)
                        {
                            // If the request comes from SignalR falling back to long polling, do not renew
                            if (context.Request.Path.Value.EndsWith("/hub"))
                            {
                                context.ShouldRenew = false;
                            }
                            else
                            {
                                var userContextService = context.HttpContext.RequestServices.GetRequiredService<IUserContextService>();
                                var identityService = context.HttpContext.RequestServices.GetRequiredService<IIdentityService>();
                                var userId = identityService.PersonIdFromPrincipal(context.Principal);
                                var userContext = await userContextService.GetUserContext(userId, context.HttpContext.Request.Host.Value);
                                identityService.SetUserContextCookie(new CurrentUserCookieContent
                                {
                                    IsApproved = userContext.IsApproved,
                                    OrganizationId = userContext.OrganizationId
                                });
                            }
                        }
                    };
                })
                .AddOpenIdConnect(oidc =>
                {
#pragma warning disable ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'
                    try
                    {
                        var configuration = configurationService.GetKeyCloakConfiguration().GetAwaiter().GetResult();
                        // TODO Should we be throwing an exception if these settings aren't found?
                        oidc.ClientId = !string.IsNullOrEmpty(configuration.ApiKey) ? configuration.ApiKey : "myadaptivecloudoidc";
                        oidc.ClientSecret = configuration.ApiSecretKey ?? "";
                        oidc.Authority = !string.IsNullOrEmpty(configuration.AuthorityKey) ? configuration.AuthorityKey : "https://sso.adaptivecloud.com/auth/realms/Test";
                    }
                    catch (Exception ex)
                    {
                        // Log the error and use default values for development
                        Console.WriteLine($"Warning: Failed to load KeyCloak configuration: {ex.Message}");
                        oidc.ClientId = "myadaptivecloudoidc";
                        oidc.ClientSecret = "";
                        oidc.Authority = "https://sso.adaptivecloud.com/auth/realms/Test";
                    }
                    oidc.RequireHttpsMetadata = true;
                    oidc.ResponseType = OpenIdConnectResponseType.Code;
                    oidc.Events = new OpenIdConnectEvents
                    {
                        OnTicketReceived = async ctx =>
                        {
                            // Add role claim
                            var ticketDbContext = ctx.HttpContext.RequestServices.GetRequiredService<MyAdaptiveCloudContext>();
                            ClaimsIdentity identity = (ClaimsIdentity)ctx.Principal.Identity;
                            var userContextService = ctx.HttpContext.RequestServices.GetRequiredService<IUserContextService>();
                            var identityService = ctx.HttpContext.RequestServices.GetRequiredService<IIdentityService>();

                            var expiresUtc = identityService.GetAuthenticationCookieExpiryTime();
                            ctx.Properties.ExpiresUtc = expiresUtc;
                            ctx.Properties.IsPersistent = expiresUtc != null;

                            string email = identity.FindFirst("preferred_username")?.Value ?? identity.FindFirst(ClaimTypes.Email)?.Value;
                            string firstName = identity.FindFirst(ClaimTypes.GivenName).Value;
                            string lastName = identity.FindFirst(ClaimTypes.Surname).Value;
                            string name = identity.FindFirst("name")?.Value;
                            string keycloakId = identity.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                            var person = await ticketDbContext.Person.FirstOrDefaultAsync(usr => usr.Email == email);

                            // if no user record found, create one
                            if (person == null)
                            {
                                person = new Person
                                {
                                    Email = email,
                                    FirstName = firstName,
                                    LastName = lastName,
                                    CreatedBy = null, // assume here that null means "auto" created?
                                    CreatedDate = DateTime.UtcNow,
                                };
                                ticketDbContext.Person.Add(person);
                                await ticketDbContext.SaveChangesAsync();
                            }
                            else
                            {
                                if (person.IsApiUser)
                                {
                                    ctx.Response.StatusCode = StatusCodes.Status403Forbidden;
                                    return;
                                }
                                else
                                {
                                    // if user in db is inactive or first/last name do not match oidc values, then update the db
                                    if (person.FirstName != firstName || person.LastName != lastName)
                                    {
                                        person.FirstName = firstName;
                                        person.LastName = lastName;
                                        ticketDbContext.Person.Update(person);
                                        await ticketDbContext.SaveChangesAsync();
                                    }
                                }
                            }

                            if (!person.IsApiUser)
                            {
                                var userContext = await userContextService.GetUserContext(person.PersonId, ctx.HttpContext.Request.Host.Host);
                                if (userContext.OrganizationId.HasValue && !string.IsNullOrEmpty(userContext.OrganizationName))
                                {
                                    await userContextService.UpdateKeyCloakAttributes(keycloakId, userContext);
                                }

                                // Change out the principal with a 'normalized' one so that we know what claims we have in our cookie
                                ctx.Principal = identityService.CookieClaimsPrincipal(userContext.UserId);
                                identityService.SetUserContextCookie(new CurrentUserCookieContent
                                {
                                    IsApproved = userContext.IsApproved,
                                    OrganizationId = userContext.OrganizationId
                                });
                            }

                            //return Task.CompletedTask;
                        },
                        OnRedirectToIdentityProvider = ctx =>
                        {
                            if (ctx.Properties.Items.TryGetValue("prompt", out string prompt))
                            {
                                // Handle requests to re-prompt for logging in again
                                ctx.ProtocolMessage.Prompt = prompt;
                            }

                            return Task.CompletedTask;
                        },
                    };
                })
                .AddHMACSigAuthAuthentication(AuthenticationMethodName, o => { })
                .AddBasicAuthAuthentication("MYAC Basic", options => { })
                .AddPolicyScheme(DefaultSchemaName, DefaultSchemaName, options =>
                {
                    options.ForwardDefaultSelector = context =>
                    {
                        string authorization = context.Request.Headers[HeaderNames.Authorization];
                        if (!string.IsNullOrEmpty(authorization))
                        {
                            if (authorization.StartsWith($"{AuthenticationMethodName} "))
                            {
                                return AuthenticationMethodName;
                            }
                            else if (authorization.StartsWith("Basic "))
                            {
                                return "MYAC Basic";
                            }
                        }

                        return CookieAuthenticationDefaults.AuthenticationScheme;
                    };
                });

            return services;
        }
    }
}