﻿using MyAdaptiveCloud.Services.Apis.Acronis;
using MyAdaptiveCloud.Services.Apis.AgentRemote;
using MyAdaptiveCloud.Services.Apis.CloudStack;
using MyAdaptiveCloud.Services.Apis.ConnectWise;
using MyAdaptiveCloud.Services.Apis.KeyCloak;
using MyAdaptiveCloud.Services.Apis.Ontap;
using MyAdaptiveCloud.Services.Apis.PowerDNS;
using MyAdaptiveCloud.Services.Apis.Tenax;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Services;
using Polly;
using Polly.Extensions.Http;

namespace MyAdaptiveCloud.Api.Startup
{
    public static class HttpClientExtensions
    {
        public static IServiceCollection ConfigureHttpClients(this IServiceCollection services, IConfigurationService configurationService, bool isDevelopment = false)
        {
            services.AddHttpClient<ICloudStackApi, CloudStackApi>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler(GetRetryPolicy())
                .AddPolicyHandler(GetCircuitBreakerPolicy());

            // Use mock ConnectWise API for development to avoid configuration issues
            if (isDevelopment)
            {
                services.AddScoped<IConnectWiseApi, MyAdaptiveCloud.Api.Mocks.MockConnectWiseApi>();
            }
            else
            {
                services.AddHttpClient<IConnectWiseApi, ConnectWiseApi>()
                    .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                    .AddPolicyHandler(GetRetryPolicy())
                    .AddPolicyHandler(GetCircuitBreakerPolicy());
            }

            // Temporarily using mock KeyCloak to allow application startup
            services.AddScoped<MyAdaptiveCloud.Services.Apis.KeyCloak.IKeyCloakApi, MyAdaptiveCloud.Api.Mocks.MockKeyCloakApi>();
            // services.AddHttpClient<IKeyCloakApi, KeyCloakApi>()
            //     .SetHandlerLifetime(TimeSpan.FromMinutes(5))
            //     .AddPolicyHandler(GetRetryPolicy())
            //     .AddPolicyHandler(GetCircuitBreakerPolicy());

            services.AddHttpClient<IPowerDNSApi, PowerDNSApi>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler(GetRetryPolicy())
                .AddPolicyHandler(GetCircuitBreakerPolicy());

            services.AddHttpClient<IAcronisApi, AcronisApi>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler(GetRetryPolicy())
                .AddPolicyHandler(GetCircuitBreakerPolicy());

            services.AddHttpClient<ITenaxApi, TenaxApi>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler(GetRetryPolicy())
                .AddPolicyHandler(GetCircuitBreakerPolicy());


            var ontapBuilder = services.AddHttpClient<IOntapApi, OntapApi>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler(GetOnTapRetryPolicy())
                .AddPolicyHandler(GetCircuitBreakerPolicy());

            var ontapConfiguration = configurationService.GetOntapConfiguration().GetAwaiter().GetResult();

            var disableOntapSsl = ontapConfiguration.DisableSslChecks;
            if (disableOntapSsl != null && Boolean.Parse(disableOntapSsl))
            {
                ontapBuilder.ConfigurePrimaryHttpMessageHandler(isp =>
                {
                    var handler = new HttpClientHandler();
                    handler.ClientCertificateOptions = ClientCertificateOption.Manual;
                    handler.ServerCertificateCustomValidationCallback = (httpRequestMessage, cert, cetChain, policyErrors) => { return true; };
                    return handler;
                });
            }

            services.AddHttpClient<IAgentRemoteApi, AgentRemoteApi>()
                .SetHandlerLifetime(TimeSpan.FromMinutes(5))
                .AddPolicyHandler(GetRetryPolicy())
                .AddPolicyHandler(GetCircuitBreakerPolicy())
                .AddHttpMessageHandler<AgentRemoteHMACDelegatingHandler>();

            return services;
        }

        private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(6, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
        }

        private static IAsyncPolicy<HttpResponseMessage> GetOnTapRetryPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(2, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
        }

        private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
        }
    }
}