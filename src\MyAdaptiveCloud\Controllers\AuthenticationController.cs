﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using MyAdaptiveCloud.Api.ViewModel;
using MyAdaptiveCloud.Services.Services;
using AutoMapper;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.Authentication;

namespace MyAdaptiveCloud.Api.Controllers
{
    [ApiExplorerSettings(IgnoreApi = true)]
    public class AuthenticationController : AuthenticatedControllerBase
    {
        private readonly ILogger _logger;
        private readonly IRegistrationService _registrationService;
        private readonly List<Permission> _permissions;
        private readonly IUserContextService _userContextService;

        public AuthenticationController(
            IUserContextService userContextService,
            ILogger<AuthenticationController> logger,
            IRegistrationService registrationService,
            IOptions<List<Permission>> permissions,
            IIdentityService identityService,
            IMapper mapper)
            : base(identityService, mapper)
        {
            _logger = logger;
            _registrationService = registrationService;
            _permissions = permissions.Value;
            _userContextService = userContextService;
        }

        [AllowAnonymous]
        [HttpGet("LoginOpenIdConnect")]
        public ActionResult LoginOpenIdConnect(string path)
        {
            _logger.LogInformation("Logging in with OpenIdConnect");
            var authenticationProperties = new AuthenticationProperties
            {
                RedirectUri = path ?? "/home"
            };

            return Challenge(authenticationProperties, OpenIdConnectDefaults.AuthenticationScheme);
        }

        [AllowAnonymous]
        [HttpGet("LoginOpenIdConnectWithPrompt")]
        public ActionResult LoginOpenIdConnectWithPrompt(string path)
        {
            _logger.LogInformation("Logging in with OpenIdConnect with prompt");
            var authenticationProperties = new AuthenticationProperties
            {
                RedirectUri = path ?? "/home"
            };
            // This effective makes the OIDC prompt again for logging in, in conjunction with some middleware configuration in Startup
            authenticationProperties.Items["prompt"] = "login";

            return Challenge(authenticationProperties, OpenIdConnectDefaults.AuthenticationScheme);
        }

        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            _logger.LogInformation("Logging out");
            await HttpContext.SignOutAsync();
            _identityService.DeleteUserContextCookie();
            return NoContent();
        }

        /// <summary>           
        ///     Sets the user context cookie in the response, or deletes it if a valid user context is not found.
        /// </summary>
        /// <remarks>
        ///     This endpoint is called when the application starts up, in order to support auto login. 
        ///     Therefore, it should allow anonymouys to support the local login screen. 
        ///     SSO would still work because if there is no auth ticket and this request returned a 401, the user would still get redirected to SSO,
        ///     and then redirected back to the app with a valid ticket.
        /// </remarks>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("userContext")]
        public async Task<UserContext> GetUserContext()
        {
            if (!string.IsNullOrEmpty(User?.Identity?.Name))
            {
                var userId = _identityService.PersonIdFromPrincipal(User);
                var userContext = await _userContextService.GetUserContext(userId, HttpContext.Request.Host.Value);
                if (userContext != null)
                {
                    await _identityService.SignInAndSetAuthenticationCookie(userContext.UserId);
                    _identityService.SetUserContextCookie(new CurrentUserCookieContent
                    {
                        IsApproved = userContext.IsApproved,
                        OrganizationId = userContext.OrganizationId
                    });
                    return userContext;
                }
                else
                {
                    _identityService.DeleteUserContextCookie();
                    return null;
                }
            }
            else
            {
                _identityService.DeleteUserContextCookie();
                return null;
            }
        }

        [HttpGet("userContext/{organizationId}")]
        public async Task<UserContext> GetUserContext([FromRoute] int organizationId)
        {
            var userId = _identityService.PersonIdFromPrincipal(User);
            var userContext = await _userContextService.GetUserContext(userId, organizationId);
            if (userContext != null)
            {
                await _identityService.SignInAndSetAuthenticationCookie(userContext.UserId);

                _identityService.SetUserContextCookie(new CurrentUserCookieContent
                {
                    IsApproved = userContext.IsApproved,
                    OrganizationId = userContext.OrganizationId
                });

                if (!userContext.IsApproved)
                    await _registrationService.SendApprovalEmail(organizationId, userId, HttpContext.Request.Host.Value);
                return userContext;
            }
            else
            {
                _identityService.DeleteUserContextCookie();
                return null;
            }
        }

        [AllowAnonymous]
        [HttpGet("permissionSchema")]
        public async Task<ActionResult<ApiDataResult<List<string>>>> GetPermissionSchema()
        {
            return await Task.FromResult(new ApiDataResult<List<string>>
            {
                Data = _permissions.Select(permission => permission.Perm.ToString()).ToList()
            });
        }
    }
}