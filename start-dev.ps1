# MyAdaptiveCloud Development Server Manager (PowerShell Version)
# Manages both .NET backend and Angular frontend applications

param(
    [switch]$Backend,
    [switch]$Frontend,
    [switch]$Help,
    [<PERSON><PERSON>("b")][switch]$B,
    [<PERSON><PERSON>("f")][switch]$F,
    [<PERSON><PERSON>("h")][switch]$H
)

# Handle aliases
if ($B) { $Backend = $true }
if ($F) { $Frontend = $true }
if ($H) { $Help = $true }

# Project paths
$ProjectRoot = Get-Location
$BackendProject = "src\MyAdaptiveCloud"
$FrontendDir = "src\MyAdaptiveCloud\ClientApp"
$BackendUrl = "https://localhost:5001"

# Process tracking
$BackendJob = $null
$FrontendJob = $null

# Function to display usage information
function Show-Help {
    Write-Host "MyAdaptiveCloud Development Server Manager" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\start-dev.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  (no args)     Start both backend and frontend applications"
    Write-Host "  -Backend, -b  Start only the .NET backend API"
    Write-Host "  -Frontend, -f Start only the Angular frontend"
    Write-Host "  -Help, -h     Show this help message"
    Write-Host ""
    Write-Host "Default URLs:"
    Write-Host "  Backend API:  https://localhost:5001"
    Write-Host "  Frontend:     http://localhost:4200 (or next available port)"
    Write-Host ""
    Write-Host "Press Ctrl+C to stop all running processes"
}

# Function to check if a directory exists
function Test-Directory {
    param($Path, $Name)
    
    if (-not (Test-Path $Path)) {
        Write-Host "Error: $Name directory not found: $Path" -ForegroundColor Red
        Write-Host "Please run this script from the project root directory."
        exit 1
    }
}

# Function to check if a port is in use
function Test-Port {
    param($Port)
    
    try {
        $listener = [System.Net.NetworkInformation.IPGlobalProperties]::GetIPGlobalProperties()
        $endpoints = $listener.GetActiveTcpListeners()
        return $endpoints | Where-Object { $_.Port -eq $Port }
    }
    catch {
        return $false
    }
}

# Function to cleanup processes on exit
function Stop-Applications {
    Write-Host "`nShutting down applications..." -ForegroundColor Yellow
    
    if ($BackendJob) {
        Write-Host "Stopping backend..."
        Stop-Job $BackendJob -ErrorAction SilentlyContinue
        Remove-Job $BackendJob -ErrorAction SilentlyContinue
    }
    
    if ($FrontendJob) {
        Write-Host "Stopping frontend..."
        Stop-Job $FrontendJob -ErrorAction SilentlyContinue
        Remove-Job $FrontendJob -ErrorAction SilentlyContinue
    }
    
    Write-Host "All applications stopped." -ForegroundColor Green
}

# Function to start the .NET backend
function Start-Backend {
    Write-Host "Starting .NET Backend API..." -ForegroundColor Blue
    
    # Check if backend directory exists
    Test-Directory $BackendProject "Backend project"
    
    # Check if port 5001 is available
    if (Test-Port 5001) {
        Write-Host "Warning: Port 5001 appears to be in use" -ForegroundColor Yellow
        Write-Host "The backend may fail to start or use a different port"
    }
    
    # Start the backend
    Write-Host "Running: dotnet run --project `"$BackendProject`""
    $BackendJob = Start-Job -ScriptBlock {
        param($ProjectPath)
        Set-Location $using:ProjectRoot
        dotnet run --project $ProjectPath
    } -ArgumentList $BackendProject
    
    Write-Host "✓ Backend started (Job ID: $($BackendJob.Id))" -ForegroundColor Green
    Write-Host "  URL: $BackendUrl"
    
    # Give backend time to start
    Start-Sleep 3
    
    # Check if backend job is still running
    if ($BackendJob.State -ne "Running") {
        Write-Host "✗ Backend failed to start" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# Function to start the Angular frontend
function Start-Frontend {
    Write-Host "Starting Angular Frontend..." -ForegroundColor Blue
    
    # Check if frontend directory exists
    Test-Directory $FrontendDir "Frontend"
    
    # Check if ng command is available
    if (-not (Get-Command ng -ErrorAction SilentlyContinue)) {
        Write-Host "Error: Angular CLI (ng) not found" -ForegroundColor Red
        Write-Host "Please install Angular CLI: npm install -g @angular/cli"
        return $false
    }
    
    # Start the frontend
    Write-Host "Running: ng serve (in $FrontendDir)"
    $FrontendJob = Start-Job -ScriptBlock {
        param($FrontendPath)
        Set-Location $using:ProjectRoot
        Set-Location $FrontendPath
        # Use echo to automatically answer prompts
        "n", "Y" | ng serve
    } -ArgumentList $FrontendDir
    
    Write-Host "✓ Frontend started (Job ID: $($FrontendJob.Id))" -ForegroundColor Green
    Write-Host "  Building... (this may take a moment)"
    
    # Give frontend time to start
    Start-Sleep 5
    
    # Check if frontend job is still running
    if ($FrontendJob.State -ne "Running") {
        Write-Host "✗ Frontend failed to start" -ForegroundColor Red
        return $false
    }
    
    Write-Host "✓ Frontend should be available shortly" -ForegroundColor Green
    Write-Host "  Check the job output for the actual URL (usually http://localhost:4200 or next available port)"
    
    return $true
}

# Set up cleanup on exit
Register-EngineEvent PowerShell.Exiting -Action { Stop-Applications }

# Handle Ctrl+C
[Console]::TreatControlCAsInput = $false
[Console]::CancelKeyPress += {
    Stop-Applications
    exit
}

# Parse command line arguments
if ($Help) {
    Show-Help
    exit 0
}

if ($Backend -and $Frontend) {
    Write-Host "Error: Cannot specify both -Backend and -Frontend" -ForegroundColor Red
    Show-Help
    exit 1
}

if ($Backend) {
    Write-Host "Starting backend only..." -ForegroundColor Blue
    if (Start-Backend) {
        Write-Host "`nBackend is running. Press Ctrl+C to stop." -ForegroundColor Green
        try {
            Wait-Job $BackendJob
        }
        finally {
            Stop-Applications
        }
    }
}
elseif ($Frontend) {
    Write-Host "Starting frontend only..." -ForegroundColor Blue
    if (Start-Frontend) {
        Write-Host "`nFrontend is running. Press Ctrl+C to stop." -ForegroundColor Green
        try {
            Wait-Job $FrontendJob
        }
        finally {
            Stop-Applications
        }
    }
}
else {
    # Default: start both applications
    Write-Host "Starting MyAdaptiveCloud Development Environment..." -ForegroundColor Blue
    Write-Host ""
    
    # Start backend first
    if (-not (Start-Backend)) {
        Write-Host "Failed to start backend. Aborting." -ForegroundColor Red
        exit 1
    }
    
    Write-Host ""
    
    # Start frontend
    if (-not (Start-Frontend)) {
        Write-Host "Failed to start frontend. Backend will continue running." -ForegroundColor Red
        Write-Host "Press Ctrl+C to stop the backend." -ForegroundColor Yellow
        try {
            Wait-Job $BackendJob
        }
        finally {
            Stop-Applications
        }
        exit 1
    }
    
    Write-Host ""
    Write-Host "🚀 Both applications are starting up!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Application URLs:" -ForegroundColor Blue
    Write-Host "  Backend API:  $BackendUrl"
    Write-Host "  Frontend:     Check job output for actual URL"
    Write-Host ""
    Write-Host "Press Ctrl+C to stop both applications" -ForegroundColor Yellow
    
    # Wait for both jobs
    try {
        Wait-Job $BackendJob, $FrontendJob
    }
    finally {
        Stop-Applications
    }
}
