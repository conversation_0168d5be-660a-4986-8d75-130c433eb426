-- Fix for missing KeyCloak configuration values
-- This script adds the required KeyCloak configuration values to the database

-- First, ensure the Configuration category exists
INSERT IGNORE INTO Configuration (Category, OrganizationId) VALUES ('KeyCloak', 0);

-- Get the configuration ID for KeyCloak category
SET @configId = (SELECT ConfigurationId FROM Configuration WHERE Category = 'KeyCloak' AND OrganizationId = 0);

-- Insert the required configuration values if they don't exist
INSERT IGNORE INTO ConfigurationValues (ConfigurationId, Name, Value, Type, IsSecret) 
VALUES 
    (@configId, 'Authority', 'https://sso.adaptivecloud.com/auth/realms/Test', 'input', 0),
    (@configId, 'ClientId', 'myadaptivecloudoidc', 'input', 0),
    (@configId, 'ClientSecret', '', 'input', 1);

-- Verify the values were inserted
SELECT cv.Name, cv.Value, cv.IsSecret 
FROM ConfigurationValues cv 
INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId 
WHERE c.Category = 'KeyCloak' AND c.OrganizationId = 0;
