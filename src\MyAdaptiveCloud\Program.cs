using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using MyAdaptiveCloud.Api.Authorization;
using MyAdaptiveCloud.Api.Hubs;
using MyAdaptiveCloud.Api.Middleware;
using MyAdaptiveCloud.Api.OpenApi;
using MyAdaptiveCloud.Api.Startup;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Data.Interceptors;
using MyAdaptiveCloud.Services.AutoMapper.Billing;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Services.Services.SignalRClient;
using System.Reflection;

const string SECRET_CONFIG = "appsettings.secret.json";
const string PERMISSIONS_CONFIG = "appsettings.permissions.json";

// Setting this path here will ensure that the path gets set to the current directory, rather than the webroot,
// when running locally. This is primarliy useful for testing locally. Once on the server,
// this should be both the webroot as well as the current directory from startup.
// Once CreateHostBuilder has finished, GetCurrentDirectory will be changed to be the webroot.
string secretConfigFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData, Environment.SpecialFolderOption.Create), SECRET_CONFIG);
var permissionsConfigFilePath = Path.Combine(Environment.CurrentDirectory, PERMISSIONS_CONFIG);

if (args != null && args.Length == 1 && args[0].Equals("-config", StringComparison.InvariantCultureIgnoreCase))
{
    SecretSettingsConfigurator.ConfigureAppSecretSettings(secretConfigFilePath);
    return;
}

var builder = WebApplication.CreateBuilder(args);
var services = builder.Services;
var isDevelopmentEnvironment = builder.Environment.IsDevelopment();

builder.Configuration
    .AddJsonFile(secretConfigFilePath, optional: true, reloadOnChange: false)
    .AddJsonFile(permissionsConfigFilePath, optional: false, reloadOnChange: false);

services
    .Configure<KestrelServerOptions>(options => { options.AllowSynchronousIO = true; })
    .AddAutoMapper(_ => { }, Assembly.GetExecutingAssembly())
    .AddAutoMapper(_ => { }, typeof(BillingMapperProfile).GetTypeInfo().Assembly)
    .AddHttpContextAccessor()
    .AddSingleton<CustomSaveChangesInterceptor>()
    .AddSingleton<ReadOnlyDeleteInterceptor>()
    .AddScopedRepositories()
    .AddScopedServices()
    .Configure<List<Permission>>(builder.Configuration.GetSection("Permissions"))
    .AddMemoryCache();

builder.ConfigureDbContexts();

#pragma warning disable ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'
var configurationService = services.BuildServiceProvider().GetService<IConfigurationService>();
#pragma warning restore ASP0000 // Do not call 'IServiceCollection.BuildServiceProvider' in 'ConfigureServices'

services
    .ConfigureHttpClients(configurationService, isDevelopmentEnvironment)
    .AddSingleton<RemoteCommandsHubClientService>()
    .AddSingleton<AgentUpgradeHubClientService>()
    .AddSingleton<UploadInstallersHubClientService>()
    .AddHostedService<SignalRBackgroundService>()
    .Configure<ForwardedHeadersOptions>(options =>
    {
        options.ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto;
        // Only loopback proxies are allowed by default.
        // Clear that restriction because forwarders are enabled by explicit configuration.
        options.KnownNetworks.Clear();
        options.KnownProxies.Clear();
    })
    .ConfigureAuthentication(configurationService)
    .AddControllers()
    .AddMvcOptions(options =>
    {
        options.ValueProviderFactories.Add(new OrgIdFromAuthValueProviderFactory());
        options.Filters.Add<HttpResponseExceptionFilter>();
    });

// In the lab and production, the Angular transpiled source files will be served from this directory
// This works along with UseSpaStaticFiles()
if (!isDevelopmentEnvironment)
{
    services.AddSpaStaticFiles(configuration => { configuration.RootPath = "ClientApp/dist"; });
}

services
    .AddAuthorization(options => { options.AddPolicy("registered", policy => policy.RequireClaim("registered", "True")); })
    .AddCustomOpenApi()
    .AddSignalR(e =>
    {
        e.EnableDetailedErrors = isDevelopmentEnvironment;
        e.MaximumReceiveMessageSize = 1024 * 1024 * 500;
    });

SetMaximumFileSizeForKernelServerAndForm(services, configurationService);

var app = builder.Build();
app.UseHttpCustomLogging();

// See WebApi error handling best practices
// https://learn.microsoft.com/en-us/aspnet/core/web-api/handle-errors?view=aspnetcore-8.0#exception-handler
if (isDevelopmentEnvironment)
{
    app.UseExceptionHandler("/api/error-development");
}
else
{
    app.UseExceptionHandler("/api/error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// This is needed if running behind a reverse proxy
// like ngrok which is great for testing while developing
app.UseForwardedHeaders();
app.UseAuthentication();

// In the lab and production, transpiled angular source files are served from the ClientApp/dist directory
// This method makes those files available to the client (internally it calls UseStaticFiles())
if (!isDevelopmentEnvironment)
{
    app.UseSpaStaticFiles();
}

app.UseRouting();
app.UseAuthorization();
app.UseHttpRequestTimeZoneMiddleware();
app.MapControllers();
app.MapCustomOpenApi();
app.MapHub<UserHub>("/hub", options => { options.AllowStatefulReconnects = true; });

// In the lab and production, requests to routes that don't start with /api, /scalar and /openapi are passed to the SPA middleware that serves the angular app
// This extension should be placed at the very end of the builder chain so it acts as a catch all for unhandled routes
if (!isDevelopmentEnvironment)
{
    string[] excludedUrlsPrefix = { "/api", "/hub", "/openapi", "/scalar" };
    app.MapWhen(
        x => !excludedUrlsPrefix.Any(url => x.Request.Path.Value.StartsWith(url, StringComparison.InvariantCultureIgnoreCase)),
        builder => { builder.UseSpa(spa => { spa.Options.SourcePath = "ClientApp/dist"; }); });
}

app.Run();

static void SetMaximumFileSizeForKernelServerAndForm(IServiceCollection services, IConfigurationService configurationService)
{
    var fileAdministration = configurationService.GetFileAdministrationConfiguration().Result;
    var maximumFileSize = fileAdministration.MaximumFileSize;
    services.Configure<KestrelServerOptions>(options =>
    {
        if (maximumFileSize != null)
        {
            options.Limits.MaxRequestBodySize = maximumFileSize;
        }

        options.AllowSynchronousIO = true;
    });

    services.Configure<FormOptions>(options =>
    {
        if (maximumFileSize != null)
        {
            options.MultipartBodyLengthLimit = (long)maximumFileSize;
        }
    });
}