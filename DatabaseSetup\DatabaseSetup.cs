using System;
using System.IO;
using MySqlConnector;

class DatabaseSetup
{
    static void Main(string[] args)
    {
        string connectionString = "Server=localhost,3306;User Id=myac;Password=*****;";
        string databaseName = "myadaptivecloud";
        
        try
        {
            // First, create the database if it doesn't exist
            using (var connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Connected to MySQL server successfully.");
                
                var createDbCommand = new MySqlCommand($"CREATE DATABASE IF NOT EXISTS {databaseName};", connection);
                createDbCommand.ExecuteNonQuery();
                Console.WriteLine($"Database '{databaseName}' created or already exists.");
            }
            
            // Now connect to the specific database and create tables
            connectionString += $"Database={databaseName};";
            using (var connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine($"Connected to database '{databaseName}' successfully.");
                
                // Read and execute the baseline schema
                string schemaPath = @"src\MyAdaptiveCloud.Data\sql\myadaptivecloud\baseline-schema.mariadb.sql";
                if (File.Exists(schemaPath))
                {
                    string schema = File.ReadAllText(schemaPath);
                    
                    // Split by changeset comments and execute each section
                    var sections = schema.Split(new string[] { "-- changeset" }, StringSplitOptions.RemoveEmptyEntries);
                    
                    foreach (var section in sections)
                    {
                        if (string.IsNullOrWhiteSpace(section)) continue;
                        
                        // Extract SQL from the section (skip the changeset header line)
                        var lines = section.Split('\n');
                        if (lines.Length < 2) continue;
                        
                        var sql = string.Join("\n", lines, 1, lines.Length - 1).Trim();
                        if (string.IsNullOrWhiteSpace(sql)) continue;
                        
                        try
                        {
                            var command = new MySqlCommand(sql, connection);
                            command.ExecuteNonQuery();
                            Console.WriteLine($"Executed: {sql.Substring(0, Math.Min(50, sql.Length))}...");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error executing SQL: {sql.Substring(0, Math.Min(50, sql.Length))}...");
                            Console.WriteLine($"Error: {ex.Message}");
                        }
                    }
                    
                    Console.WriteLine("Schema creation completed.");
                }
                else
                {
                    Console.WriteLine($"Schema file not found: {schemaPath}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}
