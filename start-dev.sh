#!/bin/bash

# MyAdaptiveCloud Development Server Manager
# Manages both .NET backend and Angular frontend applications

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(pwd)"
BACKEND_PROJECT="src/MyAdaptiveCloud"
FRONTEND_DIR="src/MyAdaptiveCloud/ClientApp"
BACKEND_URL="https://localhost:5001"

# Process tracking
BACKEND_PID=""
FRONTEND_PID=""
CLEANUP_DONE=false

# Function to display usage information
show_help() {
    echo -e "${BLUE}MyAdaptiveCloud Development Server Manager${NC}"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  (no args)     Start both backend and frontend applications"
    echo "  -b, --backend Start only the .NET backend API"
    echo "  -f, --frontend Start only the Angular frontend"
    echo "  -h, --help    Show this help message"
    echo ""
    echo "Default URLs:"
    echo "  Backend API:  https://localhost:5001"
    echo "  Frontend:     http://localhost:4200 (or next available port)"
    echo ""
    echo "Press Ctrl+C to stop all running processes"
}

# Function to check if a directory exists
check_directory() {
    local dir="$1"
    local name="$2"
    
    if [ ! -d "$dir" ]; then
        echo -e "${RED}Error: $name directory not found: $dir${NC}"
        echo "Please run this script from the project root directory."
        exit 1
    fi
}

# Function to check if a port is in use
check_port() {
    local port="$1"
    if netstat -an 2>/dev/null | grep -q ":$port.*LISTENING" || \
       ss -tuln 2>/dev/null | grep -q ":$port " || \
       lsof -i ":$port" 2>/dev/null | grep -q LISTEN; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to cleanup processes on exit
cleanup() {
    if [ "$CLEANUP_DONE" = true ]; then
        return
    fi
    
    CLEANUP_DONE=true
    echo -e "\n${YELLOW}Shutting down applications...${NC}"
    
    if [ ! -z "$BACKEND_PID" ]; then
        echo "Stopping backend (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
        wait $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        echo "Stopping frontend (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
        wait $FRONTEND_PID 2>/dev/null || true
    fi
    
    echo -e "${GREEN}All applications stopped.${NC}"
}

# Function to start the .NET backend
start_backend() {
    echo -e "${BLUE}Starting .NET Backend API...${NC}"
    
    # Check if backend directory exists
    check_directory "$BACKEND_PROJECT" "Backend project"
    
    # Check if port 5001 is available
    if check_port 5001; then
        echo -e "${YELLOW}Warning: Port 5001 appears to be in use${NC}"
        echo "The backend may fail to start or use a different port"
    fi
    
    # Start the backend
    echo "Running: dotnet run --project \"$BACKEND_PROJECT\""
    dotnet run --project "$BACKEND_PROJECT" &
    BACKEND_PID=$!
    
    echo -e "${GREEN}✓ Backend started (PID: $BACKEND_PID)${NC}"
    echo -e "  URL: ${BACKEND_URL}"
    
    # Give backend time to start
    sleep 3
    
    # Check if backend process is still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo -e "${RED}✗ Backend failed to start${NC}"
        return 1
    fi
}

# Function to start the Angular frontend
start_frontend() {
    echo -e "${BLUE}Starting Angular Frontend...${NC}"
    
    # Check if frontend directory exists
    check_directory "$FRONTEND_DIR" "Frontend"
    
    # Check if ng command is available
    if ! command -v ng &> /dev/null; then
        echo -e "${RED}Error: Angular CLI (ng) not found${NC}"
        echo "Please install Angular CLI: npm install -g @angular/cli"
        return 1
    fi
    
    # Start the frontend
    echo "Running: ng serve (in $FRONTEND_DIR)"
    cd "$FRONTEND_DIR"
    
    # Use echo to automatically answer prompts
    echo -e "n\nY" | ng serve &
    FRONTEND_PID=$!
    
    cd "$PROJECT_ROOT"
    
    echo -e "${GREEN}✓ Frontend started (PID: $FRONTEND_PID)${NC}"
    echo -e "  Building... (this may take a moment)"
    
    # Give frontend time to start
    sleep 5
    
    # Check if frontend process is still running
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo -e "${RED}✗ Frontend failed to start${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✓ Frontend should be available shortly${NC}"
    echo -e "  Check the output above for the actual URL (usually http://localhost:4200 or next available port)"
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM EXIT

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -b|--backend)
        echo -e "${BLUE}Starting backend only...${NC}"
        start_backend
        if [ $? -eq 0 ]; then
            echo -e "\n${GREEN}Backend is running. Press Ctrl+C to stop.${NC}"
            wait $BACKEND_PID
        fi
        ;;
    -f|--frontend)
        echo -e "${BLUE}Starting frontend only...${NC}"
        start_frontend
        if [ $? -eq 0 ]; then
            echo -e "\n${GREEN}Frontend is running. Press Ctrl+C to stop.${NC}"
            wait $FRONTEND_PID
        fi
        ;;
    "")
        # Default: start both applications
        echo -e "${BLUE}Starting MyAdaptiveCloud Development Environment...${NC}"
        echo ""
        
        # Start backend first
        start_backend
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to start backend. Aborting.${NC}"
            exit 1
        fi
        
        echo ""
        
        # Start frontend
        start_frontend
        if [ $? -ne 0 ]; then
            echo -e "${RED}Failed to start frontend. Backend will continue running.${NC}"
            echo -e "${YELLOW}Press Ctrl+C to stop the backend.${NC}"
            wait $BACKEND_PID
            exit 1
        fi
        
        echo ""
        echo -e "${GREEN}🚀 Both applications are starting up!${NC}"
        echo ""
        echo -e "${BLUE}Application URLs:${NC}"
        echo -e "  Backend API:  ${BACKEND_URL}"
        echo -e "  Frontend:     Check output above for actual URL"
        echo ""
        echo -e "${YELLOW}Press Ctrl+C to stop both applications${NC}"
        
        # Wait for both processes
        wait
        ;;
    *)
        echo -e "${RED}Error: Unknown option '$1'${NC}"
        echo ""
        show_help
        exit 1
        ;;
esac
