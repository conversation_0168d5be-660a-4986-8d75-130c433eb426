using MyAdaptiveCloud.Services.Apis.ConnectWise;
using MyAdaptiveCloud.Services.Apis.ConnectWise.Model;
using MyAdaptiveCloud.Services.DTOs.AdaptiveCloud;
using MyAdaptiveCloud.Services.Requests.Connectwise;
using System.Net.Http;

namespace MyAdaptiveCloud.Api.Mocks
{
    /// <summary>
    /// Mock implementation of ConnectWise API for development environments
    /// where ConnectWise integration is not needed or configured.
    /// </summary>
    public class MockConnectWiseApi : IConnectWiseApi
    {
        public Task<List<Company>> GetCompaniesByName(string keyword)
        {
            return Task.FromResult(new List<Company>());
        }

        public Task<Company> GetCompany(int companyId)
        {
            return Task.FromResult<Company>(null);
        }

        public Task CreateCompany(Company company)
        {
            return Task.CompletedTask;
        }

        public Task<CompanyType> GetCompanyType(string typeName)
        {
            return Task.FromResult(new CompanyType { Id = 1, Name = typeName });
        }

        public Task<List<Agreement>> GetAgreements(int companyId, string status = null)
        {
            return Task.FromResult(new List<Agreement>());
        }

        public Task<Agreement> GetAgreement(int agreementId)
        {
            return Task.FromResult<Agreement>(null);
        }

        public Task<List<Contact>> GetContactsByEmail(string emailAddress, int? companyId = null)
        {
            return Task.FromResult(new List<Contact>());
        }

        public Task CreateContact(Contact contact)
        {
            return Task.CompletedTask;
        }

        public Task<List<Company>> GetCompaniesByIdentifier(string identifier)
        {
            return Task.FromResult(new List<Company>());
        }

        public Task<T> GetCompanyByIdentifier<T>(string identifier) where T : new()
        {
            return Task.FromResult(new T());
        }

        public Task<Agreement> CreateAgreement(CreateAgreement agreement)
        {
            return Task.FromResult(new Agreement { Id = 1 });
        }

        public Task<List<BillingStatusRef>> GetClosedBillingStatuses()
        {
            return Task.FromResult(new List<BillingStatusRef>());
        }

        public Task<List<Invoice>> GetInvoices(int companyId, List<BillingStatusRef> billingStatuses, DateTime? fromDate = null)
        {
            return Task.FromResult(new List<Invoice>());
        }

        public Task<List<ServiceTicket>> GetServiceTickets(string ticketName)
        {
            return Task.FromResult(new List<ServiceTicket>());
        }

        public Task<List<ServiceTicketConfiguration>> GetServiceTicketConfigurations(int ticketId)
        {
            return Task.FromResult(new List<ServiceTicketConfiguration>());
        }

        public Task<List<ConfigurationModel>> GetConfigurations(ConnectWiseConfigurationRequest request)
        {
            return Task.FromResult(new List<ConfigurationModel>());
        }

        public Task<List<ConfigurationModel>> GetConfigurationsByIds(List<int> configurationIds)
        {
            return Task.FromResult(new List<ConfigurationModel>());
        }

        public Task<List<Configuration>> GetConfigurationsByName(string configurationName)
        {
            return Task.FromResult(new List<Configuration>());
        }

        public Task<List<Company>> GetCompaniesByIdentifierOrName(string searchTerm)
        {
            return Task.FromResult(new List<Company>());
        }

        public Task<Contact> GetContactById(int contactId)
        {
            return Task.FromResult<Contact>(null);
        }

        public Task<List<CompanySite>> GetCompanyPrimarySites(int companyId)
        {
            return Task.FromResult(new List<CompanySite>());
        }

        public Task<List<Contact>> GetCompaniesByEmailDomain(string emailAddress)
        {
            return Task.FromResult(new List<Contact>());
        }

        public Task<AgreementTypeRef> GetAgreementType(string typeName)
        {
            return Task.FromResult(new AgreementTypeRef { Id = 1, Name = typeName });
        }

        public Task<BillingCycleRef> GetBillingCycleType(string typeName)
        {
            return Task.FromResult(new BillingCycleRef { Id = 1, Name = typeName });
        }

        public Task<Agreement> GetAgreementByNameAndType(int companyId, string agreementName, string agreementType)
        {
            return Task.FromResult<Agreement>(null);
        }

        public Task<List<Addition>> GetAgreementProducts(int agreementId, List<string> productNames)
        {
            return Task.FromResult(new List<Addition>());
        }

        public Task<Agreement> PatchAgreement(int agreementId, Dictionary<string, object> patchData)
        {
            return Task.FromResult(new Agreement { Id = agreementId });
        }

        public Task<Invoice> GetInvoice(int invoiceId)
        {
            return Task.FromResult<Invoice>(null);
        }

        public Task<HttpResponseMessage> GetInvoicePdf(int invoiceId)
        {
            return Task.FromResult(new HttpResponseMessage(System.Net.HttpStatusCode.NotFound));
        }
    }
}
