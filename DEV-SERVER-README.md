# MyAdaptiveCloud Development Server Manager

This repository includes scripts to easily manage both the .NET backend API and Angular frontend applications during development.

## Available Scripts

### For Windows (Recommended)
- **`start-dev.bat`** - Windows batch file for native Windows support

### For Bash/WSL/Git Bash
- **`start-dev.sh`** - Bash script for Unix-like environments

## Usage

### Quick Start (Both Applications)
```bash
# Windows Command Prompt or PowerShell
start-dev.bat

# Bash/WSL/Git Bash
./start-dev.sh
```

This will start both applications:
- .NET Backend API on `https://localhost:5001`
- Angular Frontend on `http://localhost:4200` (or next available port)

### Individual Application Control

#### Backend Only
```bash
# Windows
start-dev.bat --backend
start-dev.bat -b

# Bash
./start-dev.sh --backend
./start-dev.sh -b
```

#### Frontend Only
```bash
# Windows
start-dev.bat --frontend
start-dev.bat -f

# Bash
./start-dev.sh --frontend
./start-dev.sh -f
```

#### Help
```bash
# Windows
start-dev.bat --help
start-dev.bat -h

# Bash
./start-dev.sh --help
./start-dev.sh -h
```

## What the Scripts Do

### Backend (.NET API)
- Runs `dotnet run --project "src\MyAdaptiveCloud"`
- Starts on `https://localhost:5001`
- Connects to MySQL database
- Serves API endpoints for the Angular frontend

### Frontend (Angular)
- Changes to `src\MyAdaptiveCloud\ClientApp` directory
- Runs `ng serve`
- Automatically handles port conflicts (uses next available port)
- Proxies API requests to the backend via `proxy.conf.json`

## Prerequisites

### For Backend
- .NET SDK installed
- MySQL database running and configured
- Database migrations applied (handled automatically)

### For Frontend
- Node.js installed
- Angular CLI installed globally: `npm install -g @angular/cli`
- Dependencies installed: `npm install` (in ClientApp directory)

## Troubleshooting

### Port Conflicts
If port 5001 (backend) or 4200 (frontend) are in use:
- **Backend**: The script will warn you but attempt to start anyway
- **Frontend**: Angular CLI will automatically prompt to use the next available port

### Database Issues
If the backend fails to start due to database issues:
1. Ensure MySQL is running
2. Check connection strings in `appsettings.Development.json`
3. Verify database migrations are applied

### Angular CLI Not Found
If you get "ng command not found":
```bash
npm install -g @angular/cli
```

### Permission Issues (Bash)
If you get permission denied on the bash script:
```bash
chmod +x start-dev.sh
```

## Process Management

### Windows (start-dev.bat)
- Applications run in separate command windows
- Close individual windows to stop specific applications
- Or close the main script window

### Bash (start-dev.sh)
- Applications run as background processes
- Press `Ctrl+C` to stop all applications gracefully
- Script automatically cleans up processes on exit

## Development Workflow

1. **Start Development Environment**:
   ```bash
   start-dev.bat  # or ./start-dev.sh
   ```

2. **Open Applications**:
   - Backend API: `https://localhost:5001`
   - Frontend: Check console output for actual URL (usually `http://localhost:4200`)

3. **Development**:
   - Edit backend code - automatic reload via `dotnet run`
   - Edit frontend code - automatic reload via Angular CLI
   - API calls from frontend automatically proxy to backend

4. **Stop Applications**:
   - **Windows**: Close the application windows
   - **Bash**: Press `Ctrl+C` in the terminal

## API Proxy Configuration

The Angular frontend is configured to proxy API requests to the backend:
- `/api/**` → `https://localhost:5001`
- `/hub` → `https://localhost:5001` (SignalR)
- `/signin-oidc` → `https://localhost:5001`
- `/signout-oidc` → `https://localhost:5001`

This configuration is in `src/MyAdaptiveCloud/ClientApp/src/proxy.conf.json`.

## Notes

- The scripts must be run from the project root directory (`d:\CODEZ\ipp\client-center`)
- Backend startup may take a few seconds due to database initialization
- Frontend build may take 30-60 seconds on first startup
- Both applications support hot reload during development
