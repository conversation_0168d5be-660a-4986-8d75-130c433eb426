﻿using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Data.Repositories;
using MyAdaptiveCloud.Data.Repositories.Agent;
using MyAdaptiveCloud.Data.Repositories.Agent.Action;
using MyAdaptiveCloud.Data.Repositories.AgentUpdates;
using MyAdaptiveCloud.Data.Repositories.Reports;
using MyAdaptiveCloud.Data.Repositories.SalesHubQuestions;
using MyAdaptiveCloud.Services.Apis.DDoSMitigation;
using MyAdaptiveCloud.Services.Apis.Synchronize.Model;
using MyAdaptiveCloud.Services.Authentication;
using MyAdaptiveCloud.Services.Provider;
using MyAdaptiveCloud.Services.Services;
using MyAdaptiveCloud.Services.Services.AgentManagement.MsiManagement;
using MyAdaptiveCloud.Services.Services.AgentManagement.ReleaseTags;
using MyAdaptiveCloud.Services.Services.AgentUpdates;
using MyAdaptiveCloud.Services.Services.DDoSMitigation;
using MyAdaptiveCloud.Services.Services.DeviceAlerts;
using MyAdaptiveCloud.Services.Services.PreSigned;
using MyAdaptiveCloud.Services.Services.Reports;
using MyAdaptiveCloud.Services.Services.Reports.Generation;
using MyAdaptiveCloud.Services.Services.SalesHubQuestions;
using MyAdaptiveCloud.Services.Services.SignalRClient;
using MyAdaptiveCloud.Services.Services.Tenax;
using MyAdaptiveCloud.Api.Hubs;

namespace MyAdaptiveCloud.Api.Startup
{
    public static class RepositoriesAndServicesConfigurationExtensions
    {
        public static IServiceCollection AddScopedServices(this IServiceCollection services)
        {
            services.AddScoped<IUserContextService, UserContextService>();
            services.AddScoped<IUserRoleService, UserRoleService>();
            services.AddScoped<IOrganizationService, OrganizationService>();
            services.AddScoped<IAdaptiveCloudService, AdaptiveCloudService>();
            services.AddScoped<IConnectWiseService, ConnectWiseService>();
            services.AddScoped<IConfigurationService, ConfigurationService>();
            services.AddScoped<IRegistrationService, RegistrationService>();
            services.AddScoped<IAgentService, AgentService>();
            services.AddScoped<IDeviceFolderService, DeviceFolderService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IIdentityService, IdentityService>();
            services.AddScoped<IAppSynchronizationService, AppSynchronizeService>();
            services.AddScoped<CloudInfrastructureSynchronize>();
            services.AddScoped<ConnectWiseSynchronize>();
            services.AddScoped<BillingSynchronize>();
            services.AddScoped<DataProtectionSynchronize>();
            services.AddScoped<IMenuService, MenuService>();
            services.AddScoped<ITwoFactorAuthenticationService, TwoFactorAuthenticationService>();
            services.AddScoped<INotificationsService, NotificationsService>();
            services.AddScoped<IServicesService, ServicesService>();
            services.AddScoped<IUserHubService, UserHubService>();
            services.AddScoped<IProfileService, ProfileService>();
            services.AddScoped<IWhiteLabelService, WhiteLabelService>();
            services.AddScoped<IOntapService, OntapService>();
            services.AddScoped<IFeatureFlagService, FeatureFlagService>();
            services.AddScoped<IEmailSenderService, EmailSenderService>();
            services.AddScoped<IAcCwProductMapService, AcCwProductMapService>();
            services.AddScoped<IACMappingService, ACMappingService>();
            services.AddScoped<IContactService, ContactService>();
            services.AddScoped<IOrganizationMappingService, OrganizationMappingService>();
            services.AddScoped<IAcCwVmMapService, AcCwVmMapService>();
            services.AddScoped<ILogService, LogService>();
            services.AddScoped<IScheduleService, ScheduleService>();
            services.AddScoped<IContextTimeZoneService, ContextTimeZoneService>();
            services.AddScoped<IPolicyService, PolicyService>();
            services.AddScoped<ICloudInfraService, CloudInfraService>();
            services.AddScoped<IRegexFilterService, RegexFilterService>();
            services.AddScoped<ISubscriptionsService, SubscriptionsService>();
            services.AddScoped<IBillingService, BillingService>();
            services.AddScoped<IHomeService, HomeService>();
            services.AddScoped<IAgentCommunicationService, AgentCommunicationService>();
            services.AddScoped<IDateTimeProvider, DateTimeProvider>();
            services.AddScoped<IProvisioningService, ProvisioningService>();
            services.AddScoped<IAcronisService, AcronisService>();
            services.AddScoped<IPersonService, PersonService>();
            services.AddScoped<IRegexFilterService, RegexFilterService>();
            services.AddScoped<IAgentHealthCheckService, AgentHealthCheckService>();
            services.AddScoped<IDeviceAlertTypeService, DeviceAlertTypeService>();
            services.AddScoped<ISoftwareUpdatesService, SoftwareUpdatesService>();
            services.AddScoped<IAgentInstalledSoftwareService, AgentInstalledSoftwareService>();
            services.AddScoped<IEscalationChainService, EscalationChainService>();
            services.AddScoped<IAgentActionService, AgentActionService>();
            services.AddScoped<IScheduleCalculatorService, ScheduleCalculatorService>();
            services.AddScoped<IAlertRulesService, AlertRulesService>();
            services.AddScoped<IDeviceThresholdsService, DeviceThresholdsService>();
            services.AddScoped<IDeviceAlertThresholdTypeToAlertTypeService, DeviceAlertThresholdTypeToAlertTypeService>();
            services.AddScoped<IDeviceThresholdsOverrideService, DeviceThresholdsOverrideService>();
            services.AddScoped<IFolderDeviceThresholdsService, FolderDeviceThresholdsService>();
            services.AddScoped<IOrganizationAlertDeviceThresholdService, OrganizationAlertDeviceThresholdService>();
            services.AddScoped<IDeviceAlertThresholdInheritanceService, DeviceAlertThresholdInheritanceService>();
            services.AddScoped<IDeviceAlertGeneratorService, DeviceAlertGeneratorService>();
            services.AddScoped<IDeviceAlertGeneratorManager, DeviceAlertGeneratorManager>();
            services.AddScoped<IHeartbeatAlertProcessor, HeartbeatAlertProcessor>();
            services.AddScoped<IDeviceAlertGeneratorDataService, DeviceAlertGeneratorDataService>();
            services.AddScoped<IDeviceAlertThresholdLevelService, DeviceAlertThresholdLevelService>();
            services.AddScoped<IDeviceAlertMatchAlertRuleService, DeviceAlertMatchAlertRuleService>();
            services.AddScoped<IScheduledDowntimeService, ScheduledDowntimeService>();
            services.AddScoped<IAuthenticationKeyService, AuthenticationKeyService>();
            services.AddScoped<IDeviceAlertThresholdTypeService, DeviceAlertThresholdTypeService>();
            services.AddScoped<IDeviceAlertService, DeviceAlertService>();
            services.AddScoped<IDeviceAlertEscalationChainNotifiedService, DeviceAlertEscalationChainNotifiedService>();
            services.AddScoped<IDeviceAlertEmailSenderService, DeviceAlertEmailSenderService>();
            services.AddScoped<IDeviceAlertEmailSenderDataService, DeviceAlertEmailSenderDataService>();
            services.AddScoped<IDeviceAlertAgentDownManager, DeviceAlertAgentDownManager>();
            services.AddScoped<IDeviceAlertEmailService, DeviceAlertEmailService>();
            services.AddScoped<IApiUserService, ApiUserService>();
            services.AddScoped<IFileAdministrationService, FileAdministrationService>();
            services.AddScoped<IAgentRemoteDesktopConnectionService, AgentRemoteDesktopConnectionService>();
            services.AddScoped<AgentRemoteHMACDelegatingHandler>();
            services.AddScoped<IUpdateCacheService, UpdateCacheService>();
            services.AddScoped<IFileAdministrationFolderService, FileAdministrationFolderService>();
            services.AddScoped<IPolicyUpdatesService, PolicyUpdatesService>();
            services.AddScoped<IEntityAuthorizationService, EntityAuthorizationService>();
            services.AddScoped<IInvitationCodeService, InvitationCodeService>();
            services.AddScoped<IDeviceControlService, DeviceControlService>();
            services.AddScoped<IOrganizationPreSignedService, OrganizationPreSignedService>();
            services.AddScoped<IUtilitiesService, UtilitiesService>();
            services.AddScoped<IPatchAuditReportBuilder, PatchAuditReportBuilder>();
            services.AddScoped<IPatchComplianceReportBuilder, PatchComplianceReportBuilder>();
            services.AddScoped<IPdfGenerator, PdfGenerator>();
            services.AddScoped<IReportsService, ReportsService>();
            services.AddScoped<ITenaxService, TenaxService>();
            services.AddScoped<IAgentActivationKeyService, AgentActivationKeyService>();
            services.AddScoped<IAgentTerminalWebSocketConnectionService, AgentTerminalWebSocketConnectionService>();
            services.AddScoped<ISalesHubQuestionService, SalesHubQuestionService>();
            services.AddScoped<IPartnerResourceHubService, PartnerResourceHubService>();
            services.AddScoped<IDDoSMitigationApiService, DDoSMitigationApiService>();
            services.AddScoped<IDDoSMitigationService, DDoSMitigationService>();
            services.AddScoped<IReleaseTagService, ReleaseTagService>();
            services.AddScoped<IPatchAuditReportGenerator, PatchAuditReportGenerator>();
            services.AddScoped<IPatchComplianceReportGenerator, PatchComplianceReportGenerator>();
            services.AddScoped<IMsiUploadNotificationService, MsiUploadNotificationService>();
            services.AddScoped<IMsiManagementService, MsiManagementService>();
            services.AddScoped<IOrganizationAgentsUpgradeGracePeriodService, OrganizationAgentsUpgradeGracePeriodService>();
            services.AddScoped<IAuditSubscriptionRepository, AuditSubscriptionRepository>();

            return services;
        }

        public static IServiceCollection AddScopedRepositories(this IServiceCollection services)
        {
            services.AddScoped<IOrganizationRepository, OrganizationRepository>();
            services.AddScoped<IAcCwVmMapRepository, AcCwVmMapRepository>();
            services.AddScoped<IAcMappingRepository, AcMappingRepository>();
            services.AddScoped<IAcCwProductMapRepository, AcCwProductMapRepository>();
            services.AddScoped<IAgentUpdateRepository, AgentUpdateRepository>();
            services.AddScoped<IUserRoleRepository, UserRoleRepository>();
            services.AddScoped<IOrganizationMappingRepository, OrganizationMappingRepository>();
            services.AddScoped<IOrganizationPolicyRepository, OrganizationPolicyRepository>();
            services.AddScoped<IWhiteLabelRepository, WhiteLabelRepository>();
            services.AddScoped<IContactRepository, ContactRepository>();
            services.AddScoped<IMemberRepository, MemberRepository>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IPersonRepository, PersonRepository>();
            services.AddScoped<IAgentActivationKeyRepository, AgentActivationKeyRepository>();
            services.AddScoped<IAcUsageRunLogEntryRepository, AcUsageRunLogEntryRepository>();
            services.AddScoped<ILogRepository, LogRepository>();
            services.AddScoped<IMessageQueueRepository, MessageQueueRepository>();
            services.AddScoped<IScheduleRepository, ScheduleRepository>();
            services.AddScoped<IPolicyRepository, PolicyRepository>();
            services.AddScoped<IRegexFilterRepository, RegexFilterRepository>();
            services.AddScoped<IServiceRepository, ServiceRepository>();
            services.AddScoped<IBillingRepository, BillingRepository>();
            services.AddScoped<INotificationRepository, NotificationRepository>();
            services.AddScoped<IRegexFilterRepository, RegexFilterRepository>();
            services.AddScoped<IAgentHealthCheckRepository, AgentHealthCheckRepository>();
            services.AddScoped<IAgentRepository, AgentRepository>();
            services.AddScoped<IDeviceAlertRepository, DeviceAlertRepository>();
            services.AddScoped<IDeviceAlertTypeRepository, DeviceAlertTypeRepository>();
            services.AddScoped<IAgentSoftwareInventoryAppsRepository, AgentSoftwareInventoryAppsRepository>();
            services.AddScoped<IEscalationChainRepository, EscalationChainRepository>();
            services.AddScoped<IAgentActionRepository, AgentActionRepository>();
            services.AddScoped<IAgentActionTypeRepository, AgentActionTypeRepository>();
            services.AddScoped<IDeviceFolderRepository, DeviceFolderRepository>();
            services.AddScoped<IDeviceFolderPolicyRepository, DeviceFolderPolicyRepository>();
            services.AddScoped<IAgentDeviceFolderRepository, AgentDeviceFolderRepository>();
            services.AddScoped<IDeviceAlertRulesRepository, DeviceAlertRulesRepository>();
            services.AddScoped<IDeviceAlertThresholdOverrideRepository, DeviceAlertThresholdOverrideRepository>();
            services.AddScoped<IDeviceAlertThresholdTypeToAlertTypeRepository, DeviceAlertThresholdTypeToAlertTypeRepository>();
            services.AddScoped<IHealthStatDiskRepository, HealthStatDiskRepository>();
            services.AddScoped<IDeviceAlertThresholdInheritanceTypeRepository, DeviceAlertThresholdInheritanceTypeRepository>();
            services.AddScoped<IFolderDeviceAlertThresholdRepository, FolderDeviceAlertThresholdRepository>();
            services.AddScoped<IOrganizationDeviceAlertThresholdRepository, OrganizationDeviceAlertThresholdRepository>();
            services.AddScoped<IDeviceAlertThresholdLevelRepository, DeviceAlertThresholdLevelRepository>();
            services.AddScoped<IDeviceAlertRuleComparerRepository, DeviceAlertRuleComparerRepository>();
            services.AddScoped<IDeviceAlertRuleActionRepository, DeviceAlertRuleActionRepository>();
            services.AddScoped<IAgentRemoteDesktopConnectionRepository, AgentRemoteDesktopConnectionRepository>();
            services.AddScoped<IDeviceAlertThresholdTypeRepository, DeviceAlertThresholdTypeRepository>();
            services.AddScoped<IAgentSummariesRepository, AgentSummariesRepository>();
            services.AddScoped<IScheduledDowntimeRepository, ScheduledDowntimeRepository>();
            services.AddScoped<IDeviceAlertEscalationChainNotifiedRepository, DeviceAlertEscalationChainNotifiedRepository>();
            services.AddScoped<IDeviceAlertToDeviceAlertRulesRepository, DeviceAlertToDeviceAlertRulesRepository>();
            services.AddScoped<IAdaptiveCloudDriveFileRepository, AdaptiveCloudDriveFileRepository>();
            services.AddScoped<IUpdateDeclinedRepository, UpdateDeclinedRepository>();
            services.AddScoped<IUpdateApprovedRepository, UpdateApprovedRepository>();
            services.AddScoped<IUpdateRepository, UpdateRepository>();
            services.AddScoped<IFileAdministrationFolderRepository, FileAdministrationFolderRepository>();
            services.AddScoped<IAuthenticationKeyRepository, AuthenticationKeyRepository>();
            services.AddScoped<IInvitationCodeRepository, InvitationCodeRepository>();
            services.AddScoped<IInvitationCodeRoleRepository, InvitationCodeRoleRepository>();
            services.AddScoped<IApiUserRepository, ApiUserRepository>();
            services.AddScoped<IDeviceControlRepository, DeviceControlRepository>();
            services.AddScoped<IOrganizationPreSignedRepository, OrganizationPreSignedRepository>();
            services.AddScoped<IDictionaryWordRepository, DictionaryWordRepository>();
            services.AddScoped<ISubscriptionsRepository, SubscriptionsRepository>();
            services.AddScoped<IReportsRequestRepository, ReportsRequestRepository>();
            services.AddScoped<IReportExecutionRepository, ReportExecutionRepository>();
            services.AddScoped<IConfigurationRepository, ConfigurationRepository>();
            services.AddScoped<ISalesHubQuestionTypeRepository, SalesHubQuestionTypeRepository>();
            services.AddScoped<ISalesHubQuestionRepository, SalesHubQuestionRepository>();
            services.AddScoped<IReleaseTagRepository, ReleaseTagRepository>();
            services.AddScoped<IOrganizationAgentsUpgradeGracePeriodRepository, OrganizationAgentsUpgradeGracePeriodRepository>();
            services.AddScoped<IMsrcRepository, MsrcRepository>();

            return services;
        }
    }
}